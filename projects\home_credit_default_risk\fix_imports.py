#!/usr/bin/env python3
"""
修复项目中的相对导入问题
将所有 ..module 形式的导入改为绝对导入
"""

import os
import re
from pathlib import Path

def fix_imports_in_file(file_path):
    """修复单个文件中的导入"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复相对导入模式
        patterns = [
            (r'from \.\.core\.', 'from core.'),
            (r'from \.\.data\.', 'from data.'),
            (r'from \.\.features\.', 'from features.'),
            (r'from \.\.models\.', 'from models.'),
            (r'from \.\.pipeline\.', 'from pipeline.'),
            (r'from \.core\.', 'from core.'),
            (r'from \.data\.', 'from data.'),
            (r'from \.features\.', 'from features.'),
            (r'from \.models\.', 'from models.'),
            (r'from \.pipeline\.', 'from pipeline.'),
        ]
        
        for pattern, replacement in patterns:
            content = re.sub(pattern, replacement, content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 修复了 {file_path}")
            return True
        else:
            print(f"⏭️  跳过 {file_path} (无需修复)")
            return False
            
    except Exception as e:
        print(f"❌ 修复 {file_path} 时出错: {e}")
        return False

def main():
    """主函数"""
    project_root = Path(__file__).parent
    
    # 需要修复的目录
    directories = ['core', 'data', 'features', 'models', 'pipeline']
    
    total_files = 0
    fixed_files = 0
    
    for directory in directories:
        dir_path = project_root / directory
        if dir_path.exists():
            print(f"\n🔍 检查目录: {directory}")
            
            # 遍历所有Python文件
            for py_file in dir_path.glob('*.py'):
                total_files += 1
                if fix_imports_in_file(py_file):
                    fixed_files += 1
    
    print(f"\n📊 修复完成:")
    print(f"   总文件数: {total_files}")
    print(f"   修复文件数: {fixed_files}")
    print(f"   跳过文件数: {total_files - fixed_files}")

if __name__ == "__main__":
    main()
